<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;


use Webkul\Customer\Repositories\CustomerGroupRepository;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\WishlistRepository;
use Webkul\Core\Repositories\CoreConfigRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\PersonalAccessToken;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Category\Repositories\CategoryRepository;
use Webkul\CMS\Repositories\PageRepository;
use Webkul\Theme\Repositories\ThemeCustomizationRepository;
use Webkul\MLKWebAPI\Services\OptimizedProductQueryService;

class IndexController extends APIController
{
    /**
     * CustomerGroupRepository object
     *
     * @var \Webkul\Customer\Repositories\CustomerGroupRepository
     */
    protected $customerGroupRepository;

    /**
     * CoreConfigRepository object
     *
     * @var \Webkul\Core\Repositories\CoreConfigRepository
     */
    protected $coreConfigRepository;
    
    /**
     * ProductRepository object
     *
     * @var \Webkul\Product\Repositories\ProductRepository
     */
    protected $productRepository;
    
    /**
     * CategoryRepository object
     *
     * @var \Webkul\Category\Repositories\CategoryRepository
     */
    protected $categoryRepository;

    /**
     * PageRepository object
     *
     * @var \Webkul\CMS\Repositories\PageRepository
     */
    protected $pageRepository;

    /**
     * ThemeCustomizationRepository object
     *
     * @var \Webkul\Theme\Repositories\ThemeCustomizationRepository
     */
    protected $themeCustomizationRepository;

    /**
     * BaseProductFormatter instance
     *
     * @var \Webkul\MLKWebAPI\Http\Controllers\API\BaseProductFormatter
     */
    protected $productFormatter;

    /**
     * OptimizedProductQueryService instance
     *
     * @var \Webkul\MLKWebAPI\Services\OptimizedProductQueryService
     */
    protected $optimizedProductQueryService;

    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Customer\Repositories\CustomerGroupRepository  $customerGroupRepository
     * @param  \Webkul\Core\Repositories\CoreConfigRepository  $coreConfigRepository
     * @param  \Webkul\Product\Repositories\ProductRepository  $productRepository
     * @param  \Webkul\Category\Repositories\CategoryRepository  $categoryRepository
     * @param  \Webkul\CMS\Repositories\PageRepository  $pageRepository
     * @param  \Webkul\Theme\Repositories\ThemeCustomizationRepository  $themeCustomizationRepository
     * @param  \Webkul\Customer\Repositories\CustomerRepository  $customerRepository
     * @param  \Webkul\Customer\Repositories\WishlistRepository  $wishlistRepository
     * @param  \Webkul\MLKWebAPI\Services\OptimizedProductQueryService  $optimizedProductQueryService
     * @return void
     */
    public function __construct(
        CustomerGroupRepository $customerGroupRepository,
        CoreConfigRepository $coreConfigRepository,
        ProductRepository $productRepository,
        CategoryRepository $categoryRepository,
        PageRepository $pageRepository,
        ThemeCustomizationRepository $themeCustomizationRepository,
        CustomerRepository $customerRepository,
        WishlistRepository $wishlistRepository,
        OptimizedProductQueryService $optimizedProductQueryService
    ) {
        $this->customerGroupRepository = $customerGroupRepository;
        $this->coreConfigRepository = $coreConfigRepository;
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->pageRepository = $pageRepository;
        $this->themeCustomizationRepository = $themeCustomizationRepository;
        $this->productFormatter = new BaseProductFormatter($customerRepository, $wishlistRepository);
        $this->optimizedProductQueryService = $optimizedProductQueryService;
    }

    public function index()
    {
        $isWholesale = $this->isWholesaleUser();
        $locale = core()->getCurrentLocale()->code;
        
        // 批量获取所有主题配置，避免重复查询
        $themeConfigs = $this->batchGetThemeCustomizations([
            'index_banner', 'new_arrivals', 'featured_collections', 'index_center_banner',
            'hot_sales', 'magsafe_accessories', 'additional_items', 'index_foot_banner', 'footer_links'
        ], $locale, $isWholesale);
        
        // 处理banner图片URL转换
        $this->processBannerImages($themeConfigs, ['index_banner','featured_collections', 'index_center_banner', 'index_foot_banner']);
        
        // 批量获取产品数据
        $productSections = [
            'new_arrivals' => $themeConfigs['new_arrivals'],
            // 'featured_collections' => $themeConfigs['featured_collections'],
            'hot_sales' => $themeConfigs['hot_sales'],
            'magsafe_accessories' => $themeConfigs['magsafe_accessories'],
            'additional_items' => $themeConfigs['additional_items'],
        ];
        
        $products = $this->batchGetProductsByOptions($productSections);
        
        // 获取关于我们的内容和新闻（这些相对独立，保持原逻辑）
        $aboutUs = $this->getAboutUsContent();
        $news = $this->getNews();
        
        $data = [
            'is_wholesale' => $isWholesale,
            'homepage' => [
                'index_banner' => [
                    'data' => $themeConfigs['index_banner'],
                ],
                'new_arrivals' => [
                    'title' => $themeConfigs['new_arrivals']['title'] ?? '',
                    'description' => '',
                    'data' => $products['new_arrivals'],
                ],
                'featured_collections' => [
                    'title' => trans('mlk::app.api.index.featured_collections'),
                    'description' => '',
                    'data' => $themeConfigs['featured_collections'],
                ],
                'index_center_banner' => [
                    'data' => $themeConfigs['index_center_banner'],
                ],
                'hot_sales' => [
                    'title' => $themeConfigs['hot_sales']['title'] ?? '',
                    'description' => '',
                    'data' => $products['hot_sales'],
                ],
                'magsafe_accessories' => [
                    'title' => $themeConfigs['magsafe_accessories']['title'] ?? '',
                    'description' => '',
                    'data' => $products['magsafe_accessories'],
                ],
                'additional_items' => [
                    'title' => $themeConfigs['additional_items']['title'] ?? '',
                    'description' => '',
                    'data' => $products['additional_items'],
                ],
                'index_foot_banners' => $themeConfigs['index_foot_banner'],
                'about_us' => [
                    'title' => trans('mlk::app.api.index.about_mlk'),
                    'description' => '',
                    'data' => $aboutUs,
                ],
                'news' => [
                    'title' => trans('mlk::app.api.index.discover'),
                    'description' => '',
                    'data' => $news,
                ],
                'footer_links' => $themeConfigs['footer_links'],
            ],
        ];
        
        return $this->success($data);
    }
    
    /**
     * 获取主题自定义
     *
     * @param string $name
     * @param string $locale
     * @param bool $isWholesale
     * @return array
     */
    protected function getThemeCustomization($name,$locale, $isWholesale)
    {
        // 根据是否批发客户确定查询的主题名称
        $themeName = $isWholesale ? 'wholesale_'.$name : $name;
        // 获取当前渠道ID
        $channelId = core()->getCurrentChannel()->id;

        // 查询主题自定义表
        $themeCustomization = $this->themeCustomizationRepository->findWhere([
            'name' => $themeName,
            'channel_id' => $channelId,
            'status' => 1
        ])->first();

        if (!$themeCustomization) {
            return [];
        }
       
        // 获取当前语言的翻译
        $translation = $themeCustomization->translate($locale);
        // 如果当前语言没有翻译，则使用zh_CN翻译
        if (!$translation) {
            $translation = $themeCustomization->translate('en');
        } 
        if (!isset($translation->options)) {
            return [];
        }
        return $translation->options;
    }
    

    protected function getProductsByOptions($options)
    {
        if (!isset($options['filters']) || empty($options['filters'])) {
            return [];
        }
        $filters = $options['filters'];
        
        // 获取当前渠道和客户组信息
        $currentChannel = core()->getCurrentChannel();
        $customerGroup = app('Webkul\Customer\Repositories\CustomerRepository')->getCurrentGroup();
        
        // 查询产品时就预加载所有必要的关联数据
        $products = $this->productRepository->with([
            // 基础关联
            'images',
            'videos',
            'categories',
            'attribute_family',
            'attribute_values.attribute',
            
            // 价格相关关联 - 过滤当前渠道和客户组
            'price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id);
            },
            'customer_group_prices' => function ($query) use ($customerGroup) {
                $query->where('customer_group_id', $customerGroup->id)
                      ->orderBy('qty', 'asc');
            },
            'catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id)
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            
            // 库存关联 - 过滤当前渠道
            'inventory_indices' => function ($query) use ($currentChannel) {
                $query->where('channel_id', $currentChannel->id);
            },
            
            // 可配置产品相关
            'super_attributes.options',
            'variants' => function ($query) {
                $query->with([
                    'images',
                    'attribute_values.attribute',
                ]);
            },
            'variants.price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id);
            },
            'variants.customer_group_prices' => function ($query) use ($customerGroup) {
                $query->where('customer_group_id', $customerGroup->id)
                      ->orderBy('qty', 'asc');
            },
            'variants.catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id)
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            'variants.inventory_indices' => function ($query) use ($currentChannel) {
                $query->where('channel_id', $currentChannel->id);
            },
            
            // 评论关联
            'reviews'
        ])->getAll(array_merge($filters, [
            'channel_id' => $currentChannel->id,
            'status' => 1,
            'visible_individually' => 1,
        ]));
        
        // 如果没有产品，直接返回空数组
        if ($products->isEmpty()) {
            return [];
        }
        
        // 批量获取当前用户的wishlist产品ID以避免N+1查询
        $this->preloadUserWishlistData($products);
        
        // 使用ProductResource格式化产品数据
        $formattedProducts = $products->map(function ($product) {
            return (new \Webkul\MLKWebAPI\Http\Resources\ProductResource($product))->toArray(request());
        })->toArray();
        
        return $formattedProducts;
    }
    
    /**
     * 批量预加载用户wishlist数据以避免N+1查询
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @return void
     */
    protected function preloadUserWishlistData($products)
    {
        // 检查用户是否已登录
        $user = auth()->guard('sanctum')->user();
        if (!$user) {
            return;
        }
        
        try {
            // 获取当前用户在当前渠道的所有wishlist产品ID
            $wishlistProductIds = app('Webkul\Customer\Repositories\WishlistRepository')
                ->findWhere([
                    'customer_id' => $user->id,
                    'channel_id' => core()->getCurrentChannel()->id,
                ])
                ->pluck('product_id')
                ->toArray();
            
            // 将wishlist信息缓存到产品对象中，避免后续查询
            $products->each(function ($product) use ($wishlistProductIds) {
                $product->setRelation('user_wishlist_status', in_array($product->id, $wishlistProductIds));
            });
            
        } catch (\Exception $e) {
            // 如果获取失败，记录错误但继续执行
            logger()->warning('Failed to preload user wishlist data: ' . $e->getMessage());
        }
    }

    /**
     * 批量获取主题配置，避免重复查询
     *
     * @param array $themeNames
     * @param string $locale
     * @param bool $isWholesale
     * @return array
     */
    protected function batchGetThemeCustomizations(array $themeNames, string $locale, bool $isWholesale): array
    {
        $results = [];
        
        // 构建查询的主题名称列表
        $queryNames = [];
        foreach ($themeNames as $name) {
            $queryNames[] = $isWholesale ? 'wholesale_' . $name : $name;
        }
        
        // 批量查询所有主题配置
        $themeCustomizations = $this->themeCustomizationRepository->findWhere([
            ['channel_id', '=', core()->getCurrentChannel()->id],
            ['status', '=', 1]
        ])->whereIn('name', $queryNames);
        
        // 组织结果数组
        foreach ($themeNames as $originalName) {
            $themeName = $isWholesale ? 'wholesale_' . $originalName : $originalName;
            $theme = $themeCustomizations->where('name', $themeName)->first();
            
            if ($theme) {
                $translation = $theme->translate($locale) ?: $theme->translate('en');
                $results[$originalName] = $translation->options ?? [];
            } else {
                $results[$originalName] = [];
            }
        }
        
        return $results;
    }

    /**
     * 处理banner图片URL转换
     *
     * @param array &$themeConfigs
     * @param array $bannerKeys
     * @return void
     */
    protected function processBannerImages(array &$themeConfigs, array $bannerKeys): void
    {
        foreach ($bannerKeys as $key) {
            if (isset($themeConfigs[$key]['images']) && !empty($themeConfigs[$key]['images'])) {
                foreach ($themeConfigs[$key]['images'] as $imgKey => $bannerImage) {
                    $themeConfigs[$key]['images'][$imgKey]['image'] = url($bannerImage['image']);
                }
            }
        }
    }

    /**
     * 批量获取产品数据，合并所有产品查询
     *
     * @param array $productSections
     * @return array
     */
    protected function batchGetProductsByOptions(array $productSections): array
    {
        $results = [];
        $currentChannel = core()->getCurrentChannel();
        $customerGroup = app('Webkul\Customer\Repositories\CustomerRepository')->getCurrentGroup();
        
        // 收集所有需要查询的产品ID
        $allProductIds = [];
        $sectionFilters = [];
        
        foreach ($productSections as $sectionName => $options) {
            if (!isset($options['filters']) || empty($options['filters'])) {
                $results[$sectionName] = [];
                continue;
            }
            
            $filters = $options['filters'];
            $sectionFilters[$sectionName] = $filters;
            
            // 如果有产品ID筛选，收集这些ID
            if (isset($filters['product_id'])) {
                if (is_array($filters['product_id'])) {
                    $allProductIds = array_merge($allProductIds, $filters['product_id']);
                } else {
                    $allProductIds[] = $filters['product_id'];
                }
            }
        }
        
        // 如果有具体的产品ID，批量查询这些产品
        if (!empty($allProductIds)) {
            $allProductIds = array_unique($allProductIds);
            
            $products = $this->productRepository->with([
                'images', 'videos', 'categories', 'attribute_family', 'attribute_values.attribute',
                'price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                    $query->where('channel_id', $currentChannel->id)
                          ->where('customer_group_id', $customerGroup->id);
                },
                'customer_group_prices' => function ($query) use ($customerGroup) {
                    $query->where('customer_group_id', $customerGroup->id)->orderBy('qty', 'asc');
                },
                'catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                    $query->where('channel_id', $currentChannel->id)
                          ->where('customer_group_id', $customerGroup->id)
                          ->where('rule_date', now()->format('Y-m-d'));
                },
                'inventory_indices' => function ($query) use ($currentChannel) {
                    $query->where('channel_id', $currentChannel->id);
                },
                'super_attributes.options.translations',
                'variants' => function ($query) {
                    $query->with(['images', 'attribute_values.attribute']);
                },
                'variants.price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                    $query->where('channel_id', $currentChannel->id)
                          ->where('customer_group_id', $customerGroup->id);
                },
                'variants.customer_group_prices' => function ($query) use ($customerGroup) {
                    $query->where('customer_group_id', $customerGroup->id)->orderBy('qty', 'asc');
                },
                'variants.catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                    $query->where('channel_id', $currentChannel->id)
                          ->where('customer_group_id', $customerGroup->id)
                          ->where('rule_date', now()->format('Y-m-d'));
                },
                'variants.inventory_indices' => function ($query) use ($currentChannel) {
                    $query->where('channel_id', $currentChannel->id);
                },
                'reviews'
            ])->findWhere([
                ['channel_id', '=', $currentChannel->id],
                ['status', '=', 1],
                ['visible_individually', '=', 1]
            ])->whereIn('id', $allProductIds);
            
            // 批量预加载用户wishlist数据
            $this->preloadUserWishlistData($products);
            
            // 按产品ID索引
            $productsByIds = $products->keyBy('id');
            
            // 为每个section分配对应的产品
            foreach ($sectionFilters as $sectionName => $filters) {
                if (isset($filters['product_id'])) {
                    $sectionProductIds = is_array($filters['product_id']) ? $filters['product_id'] : [$filters['product_id']];
                    $sectionProducts = collect();
                    
                    foreach ($sectionProductIds as $productId) {
                        if (isset($productsByIds[$productId])) {
                            $sectionProducts->push($productsByIds[$productId]);
                        }
                    }
                    
                    $results[$sectionName] = $sectionProducts->map(function ($product) {
                        return (new \Webkul\MLKWebAPI\Http\Resources\ProductResource($product))->toArray(request());
                    })->toArray();
                } else {
                    $results[$sectionName] = [];
                }
            }
        } else {
            // 如果没有具体产品ID，使用原来的方法
            foreach ($productSections as $sectionName => $options) {
                $results[$sectionName] = $this->getProductsByOptions($options);
            }
        }
        
        return $results;
    }







    /**
     * 获取分类
     *
     * @param string $configType
     * @param bool $isWholesale
     * @return array
     */
    protected function getCategories($configType, $isWholesale)
    {
        // 获取当前渠道的根分类ID
        $rootCategoryId = core()->getCurrentChannel()->root_category_id;
        
        // 获取根分类下的所有子分类树
        $categoryTree = $this->categoryRepository->getVisibleCategoryTree($rootCategoryId);
        
        // 转换分类为统一格式
        $formattedCategories = $this->formatCategories($categoryTree);
        
        return $formattedCategories;
    }
    
    /**
     * 将分类转换为统一格式
     *
     * @param array $categories
     * @param int $startPosition
     * @return array
     */
    protected function formatCategories($categories, $startPosition = 1)
    {
        $formattedCategories = [];
        $position = $startPosition;
        
        foreach ($categories as $category) {
            $categoryUrl = '/category/' . $category->slug;
            
            $formattedCategory = [
                'name' => $category->name,
                'link' => $categoryUrl,
                'position' => $position++,
                'logo_path' => $category->logo_url ?? '',
                'children' => [],
            ];
            
            // 递归处理子分类
            if (!empty($category->children)) {
                $formattedCategory['children'] = $this->formatCategories($category->children, 1);
            }
            
            $formattedCategories[] = $formattedCategory;
        }
        
        return $formattedCategories;
    }

    /**
     * 获取关于我们的内容
     *
     * @return array
     */
    protected function getAboutUsContent()
    {
        // 获取ID为1的CMS页面
        $page = $this->pageRepository->find(1);
        
        if (!$page) {
            return [
                'title' => trans('mlk::app.api.index.about_mlk'),
                'description' => '',
                'content' => '',
            ];
        }
        
        // 获取当前区域的翻译内容
        $locale = core()->getCurrentLocale()->code;
        
        return [
            'title' => $page->translate($locale)->meta_title ?? trans('mlk::app.api.index.about_mlk'),
            'description' => $page->translate($locale)->meta_description ?? '',
            'content' => strip_tags($page->translate($locale)->html_content ?? ''),
        ];
    }

    /**
     * 获取新闻文章
     *
     * @return array
     */
    protected function getNews()
    {
        // 获取当前区域的翻译内容
        $locale = core()->getCurrentLocale()->code;
        
        // 通过模型进行查询
        $model = app('Webkul\CMS\Contracts\Page');
        
        // 查询带有标签的文章，按创建时间降序排序
        $pages = $model->whereHas('tags', function($query) {
                // 确保有标签关联
                $query->whereNotNull('id');
            })
            ->with(['tags', 'translations'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        $newsItems = [];
        
        foreach ($pages as $page) {
            $translation = $page->translate($locale);
            
            if (!$translation) {
                continue;
            }
            
            $htmlContent = $translation->html_content ?? '';
            $thumbnailUrl = $this->extractFirstImageUrl($htmlContent);
            
            // 如果没有提取到图片，使用默认图
            if (!$thumbnailUrl) {
                $thumbnailUrl = asset('vendor/webkul/admin/assets/images/product/meduim-product-placeholder.png');
            }
            
            $tags = [];
            foreach ($page->tags as $tag) {
                $tags[] = [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'slug' => $tag->slug,
                ];
            }
            
            $newsItems[] = [
                'id' => $page->id,
                'title' => $translation->page_title ?? $translation->meta_title ?? '',
                'url_key' => $translation->url_key ?? '',
                'description' => empty($translation->meta_description) ? trim(substr(strip_tags($htmlContent), 0, 160)) : $translation->meta_description,
                //'content' => strip_tags($htmlContent),
                'thumbnail' => $thumbnailUrl,
                'published_at' => $page->created_at->format('Y-m-d H:i:s'),
                'tags' => $tags,
            ];
        }
        
        return $newsItems;
    }
    
    /**
     * 从HTML内容中提取第一张图片的URL
     *
     * @param string $htmlContent
     * @return string|null
     */
    protected function extractFirstImageUrl($htmlContent)
    {
        if (empty($htmlContent)) {
            return null;
        }
        
        // 使用正则表达式匹配第一个<img>标签的src属性
        preg_match('/<img.+?src=[\'"](?P<src>.+?)[\'"].*?>/i', $htmlContent, $matches);
        
        if (isset($matches['src'])) {
            return $matches['src'];
        }
        
        return null;
    }

    /**
     * 获取分类产品
     *
     * @param array $categories 分类列表
     * @param bool $isWholesale 是否是批发客户
     * @return array
     */
    protected function getProducts($categories, $isWholesale)
    {
        $result = [];
        $limit = 16; // 每个分类获取16条产品
        
        // 如果不是批发客户，只查询第一个分类
        $categoriesToProcess = $isWholesale ? $categories : array_slice($categories, 0, 1);
        foreach ($categoriesToProcess as $category) {
            // 从URL中提取分类slug
            $categorySlug = '';
            if (!empty($category['link'])) {
                $parts = explode('/', $category['link']);
                $categorySlug = end($parts);
            }
            
            if (empty($categorySlug)) {
                continue;
            }
            
            // 根据slug获取分类
            $categoryModel = app('Webkul\Category\Contracts\Category')->whereTranslation('slug', $categorySlug)->first();
            
            if (!$categoryModel) {
                continue;
            }
            
            // 使用产品查询构建器获取分类产品
            $params = [
                'category_id' => $categoryModel->id,
                'limit' => $limit,
                'order' => 'desc',
                'sort' => 'created_at',
            ];
            
            $categoryProducts = $this->productRepository->getAll($params);
            
            // 预加载产品关系以优化性能，包括价格计算相关的关系
            $this->productFormatter->preloadProductPriceData($categoryProducts);
            
            // 格式化产品数据
            $formattedProducts = $this->productFormatter->formatProducts($categoryProducts);
            
            // 添加到结果中
            $result[] = [
                'category' => [
                    'id' => $categoryModel->id,
                    'name' => $category['name'],
                    'slug' => $categorySlug,
                    'link' => $category['link'],
                ],
                'products' => $formattedProducts,
            ];
        }
        
        return $result;
    }

    /**
     * 获取支付图标列表
     *
     * @return array
     */
    public function paymentIcons()
    {
        $locale = core()->getCurrentLocale()->code;
        $cacheKey = "payment_icons_{$locale}";
        
        // 尝试从缓存获取数据，缓存60分钟
        $paymentIcons = Cache::remember($cacheKey, 3600, function () use ($locale) {
            // 查询pay_icons配置
            $paymentIconsRecord = $this->themeCustomizationRepository->where('name', 'pay_icons')->first();
            if (!$paymentIconsRecord) {
                return null;
            }
            
            $paymentIconsData = $paymentIconsRecord->translate($locale)->options;
            
            // 处理图片url
            if (isset($paymentIconsData['images']) && is_array($paymentIconsData['images'])) {
                foreach ($paymentIconsData['images'] as $key => $paymentIcon) {
                    if (isset($paymentIcon['image'])) {
                        $paymentIconsData['images'][$key]['image'] = url($paymentIcon['image']);
                    }
                }
            }
            
            return $paymentIconsData;
        });
        
        if (!$paymentIcons) {
            return $this->error('not found');
        }
        
        return $this->success($paymentIcons);
    }

    /**
     * 清除支付图标缓存
     * 可在主题配置更新时调用此方法
     *
     * @return void
     */
    public function clearPaymentIconsCache()
    {
        $locales = core()->getAllLocales();
        
        foreach ($locales as $locale) {
            $cacheKey = "payment_icons_{$locale->code}";
            Cache::forget($cacheKey);
        }
    }
}
