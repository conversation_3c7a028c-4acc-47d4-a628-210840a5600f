# Bagisto 性能优化指南

## 概述

根据debugbar日志分析，我们发现了严重的数据库查询性能问题。主要问题包括：
- 缺少必要的数据库索引，导致查询耗时长达数秒
- N+1查询问题，产生大量重复查询
- 查询策略不优化，缺乏批量加载机制

## 已实施的优化方案

### 1. 数据库索引优化

创建了全面的索引优化迁移文件：`database/migrations/2025_01_05_000001_add_performance_indexes.php`

主要添加的索引包括：
- `channels` 表的 `hostname` 索引（原查询耗时5.15秒）
- `channel_locales` 表的复合索引（原查询耗时3.58秒）
- `attribute_translations` 和 `attribute_option_translations` 表的复合索引
- `product_categories`、`category_translations` 等关联表的索引
- `product_attribute_values` 表的多个复合索引，优化属性查询
- 价格和库存相关表的复合索引

### 2. 查询优化服务

创建了 `OptimizedProductQueryService` 服务类，实现了：
- 使用查询构建器替代Eloquent，减少模型开销
- 批量预加载所有关联数据，避免N+1查询
- 优化的JOIN策略，使用INNER JOIN替代LEFT JOIN
- 子查询优化属性过滤，避免多次JOIN

### 3. 控制器优化

修改了 `IndexController`：
- 使用优化的查询服务替代原有的复杂查询
- 简化了 `getProductsByOptions` 方法
- 减少了代码复杂度，提高了可维护性

### 4. 数据库连接优化

更新了 `config/database.php`，添加了：
- 持久连接支持
- 查询超时设置
- 预处理语句缓存
- 查询缓冲优化

## 应用优化步骤

### 1. 运行数据库迁移

```bash
php artisan migrate
```

这将创建所有必要的数据库索引。

### 2. 分析表统计信息（可选）

如果使用MySQL，可以运行以下命令更新表统计信息：

```bash
php artisan db:optimize
```

或直接在MySQL中执行：

```sql
ANALYZE TABLE channels, channel_locales, attribute_families, attribute_translations, 
              attribute_option_translations, product_categories, category_translations, 
              customer_groups, theme_customizations, product_attribute_values, 
              product_price_indices, catalog_rule_product_prices, product_customer_group_prices,
              product_images, product_videos, product_inventory_indices, personal_access_tokens,
              attribute_group_mappings, product_channels, product_super_attributes,
              cms_pages, cms_page_translations;
```

### 3. 清理缓存

```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### 4. 重启服务

重启PHP-FPM或Web服务器以应用数据库连接优化：

```bash
# 对于 PHP-FPM
sudo service php8.2-fpm restart

# 对于 Apache
sudo service apache2 restart

# 对于 Nginx + PHP-FPM
sudo service nginx restart
sudo service php8.2-fpm restart
```

## 性能监控

### 1. 检查慢查询

启用MySQL慢查询日志：

```sql
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow-query.log';
```

### 2. 使用Laravel Debugbar

在开发环境中继续使用debugbar监控查询性能：

```php
// .env
DEBUGBAR_ENABLED=true
```

### 3. 查看索引使用情况

使用EXPLAIN分析查询是否使用了索引：

```sql
EXPLAIN SELECT * FROM channels WHERE hostname = 'example.com';
```

## 预期改进

根据优化措施，预期可以获得以下改进：
- 查询时间从秒级降低到毫秒级
- 减少90%以上的重复查询
- 降低数据库服务器负载
- 提升API响应速度3-5倍

## 进一步优化建议

1. **启用查询缓存**（如Redis）：
   ```php
   // 在 .env 中配置
   DB_CACHE_ENABLED=true
   DB_CACHE_DRIVER=redis
   ```

2. **使用Elasticsearch**：
   对于产品搜索，考虑使用Elasticsearch替代数据库查询

3. **数据库读写分离**：
   配置主从数据库，将读操作分散到从库

4. **CDN加速**：
   对产品图片等静态资源使用CDN

5. **应用层缓存**：
   对热点数据实施应用层缓存策略

## 回滚方案

如果需要回滚优化：

```bash
# 回滚数据库迁移
php artisan migrate:rollback

# 恢复原始代码
git checkout -- packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php
git checkout -- config/database.php
```

## 注意事项

1. 在生产环境应用前，请先在测试环境验证
2. 确保有完整的数据库备份
3. 监控应用日志，观察是否有异常
4. 逐步应用优化，便于定位问题

---

最后更新时间：2025-01-05