<?php

namespace Webkul\MLKWebAPI\Providers;

use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use Webkul\MLKWebAPI\Http\Middleware\APIMiddleware;
use Webkul\MLKWebAPI\Http\Middleware\LocaleMiddleware;
use Webkul\MLKWebAPI\Http\Middleware\APIAuthenticate;
use Webkul\MLKWebAPI\Console\Commands\OptimizeDatabaseCommand;

class MLKWebAPIServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     *
     * @param  \Illuminate\Routing\Router  $router
     * @return void
     */
    public function boot(Router $router)
    {
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        // 注册LocaleMiddleware中间件
        $router->aliasMiddleware('api_locale', LocaleMiddleware::class);
        
        // 注册APIAuthenticate中间件
        $router->aliasMiddleware('api_auth', APIAuthenticate::class);

        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');

        $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'mlk');

        $this->loadViewsFrom(__DIR__ . '/../Resources/views', 'mlk');

        Event::listen('bagisto.admin.layout.head', function($viewRenderEventManager) {
            $viewRenderEventManager->addTemplate('mlk::admin.layouts.style');
        });
        
        // 注册控制台命令
        if ($this->app->runningInConsole()) {
            $this->commands([
                OptimizeDatabaseCommand::class,
            ]);
        }
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->registerConfig();
        
        // 注册优化服务提供者
        $this->app->register(OptimizationServiceProvider::class);
    }

    /**
     * Register package config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->mergeConfigFrom(
            dirname(__DIR__) . '/Config/admin-menu.php', 'menu.admin'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__) . '/Config/acl.php', 'acl'
        );
        
        // 注册Sanctum配置
        $this->mergeConfigFrom(
            dirname(__DIR__) . '/Config/sanctum.php', 'sanctum'
        );
    }
}