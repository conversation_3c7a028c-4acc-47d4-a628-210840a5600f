<?php

return [
    /**
     * 数据库查询优化配置
     */
    'optimization' => [
        /**
         * 查询超时设置（秒）
         * 防止慢查询长时间占用资源
         */
        'query_timeout' => 30,
        
        /**
         * 批量查询限制
         * 防止一次查询过多数据导致内存溢出
         */
        'chunk_size' => 1000,
        
        /**
         * 启用查询日志
         * 生产环境建议关闭
         */
        'enable_query_log' => env('DB_QUERY_LOG', false),
        
        /**
         * 慢查询阈值（毫秒）
         * 超过此时间的查询会被记录
         */
        'slow_query_threshold' => 1000,
    ],

    /**
     * MySQL特定优化
     */
    'mysql' => [
        /**
         * 连接选项
         */
        'options' => [
            // 设置SQL模式，避免ONLY_FULL_GROUP_BY错误
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'",
            
            // 启用持久连接
            PDO::ATTR_PERSISTENT => true,
            
            // 设置超时
            PDO::ATTR_TIMEOUT => 30,
            
            // 启用预处理语句缓存
            PDO::ATTR_EMULATE_PREPARES => false,
            
            // 启用查询缓存
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
        ],

        /**
         * 索引优化建议
         */
        'indexes' => [
            'channels' => ['hostname'],
            'channel_locales' => ['channel_id', 'locale_id'],
            'attribute_translations' => ['attribute_id', 'locale'],
            'attribute_option_translations' => ['attribute_option_id', 'locale'],
            'product_categories' => ['product_id', 'category_id'],
            'category_translations' => ['category_id', 'locale'],
            'customer_groups' => ['code'],
            'theme_customizations' => ['channel_id', 'status'],
            'product_attribute_values' => ['product_id', 'attribute_id', 'locale'],
            'product_price_indices' => ['product_id', 'customer_group_id', 'channel_id'],
            'catalog_rule_product_prices' => ['product_id', 'channel_id', 'customer_group_id', 'rule_date'],
            'product_images' => ['product_id', 'position'],
            'product_inventory_indices' => ['product_id', 'channel_id'],
        ],

        /**
         * 查询优化器提示
         */
        'optimizer_hints' => [
            // 强制使用索引
            'force_index' => true,
            
            // 启用JOIN缓冲区
            'join_buffer_size' => '8M',
            
            // 排序缓冲区大小
            'sort_buffer_size' => '4M',
            
            // 临时表大小
            'tmp_table_size' => '64M',
            'max_heap_table_size' => '64M',
        ],
    ],

    /**
     * 查询缓存配置
     */
    'cache' => [
        /**
         * 是否启用查询结果缓存
         */
        'enabled' => env('DB_CACHE_ENABLED', true),
        
        /**
         * 缓存驱动
         */
        'driver' => env('DB_CACHE_DRIVER', 'redis'),
        
        /**
         * 缓存时间（分钟）
         */
        'ttl' => [
            'products' => 30,
            'categories' => 60,
            'attributes' => 120,
            'configs' => 240,
        ],
        
        /**
         * 缓存标签
         */
        'tags' => [
            'products' => ['products', 'catalog'],
            'categories' => ['categories', 'catalog'],
            'attributes' => ['attributes', 'catalog'],
        ],
    ],

    /**
     * 连接池配置
     */
    'pool' => [
        /**
         * 最小连接数
         */
        'min_connections' => 2,
        
        /**
         * 最大连接数
         */
        'max_connections' => 10,
        
        /**
         * 连接空闲时间（秒）
         */
        'idle_timeout' => 60,
        
        /**
         * 连接最大生命周期（秒）
         */
        'max_lifetime' => 3600,
    ],
];