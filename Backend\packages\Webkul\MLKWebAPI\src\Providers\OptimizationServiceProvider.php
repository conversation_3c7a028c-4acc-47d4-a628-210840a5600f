<?php

namespace Webkul\MLKWebAPI\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Webkul\MLKWebAPI\Services\OptimizedProductQueryService;

class OptimizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册优化的产品查询服务为单例
        $this->app->singleton(OptimizedProductQueryService::class, function ($app) {
            return new OptimizedProductQueryService(
                $app->make('Webkul\Product\Repositories\ProductRepository'),
                $app->make('Webkul\Attribute\Repositories\AttributeRepository')
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 注册数据库查询监听器（仅在调试模式下）
        if (config('app.debug') && config('database_optimization.optimization.enable_query_log', false)) {
            DB::listen(function ($query) {
                $slowQueryThreshold = config('database_optimization.optimization.slow_query_threshold', 1000);
                
                // 如果查询时间超过阈值，记录到日志
                if ($query->time > $slowQueryThreshold) {
                    Log::warning('Slow query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time . 'ms',
                        'connection' => $query->connectionName,
                    ]);
                }
            });
        }

        // 设置查询超时
        if ($timeout = config('database_optimization.optimization.query_timeout')) {
            DB::statement("SET SESSION max_execution_time = " . ($timeout * 1000));
        }
    }
}