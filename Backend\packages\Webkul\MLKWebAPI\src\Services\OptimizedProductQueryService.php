<?php

namespace Webkul\MLKWebAPI\Services;

use Webkul\Product\Repositories\ProductRepository;
use Webkul\Attribute\Repositories\AttributeRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class OptimizedProductQueryService
{
    public function __construct(
        protected ProductRepository $productRepository,
        protected AttributeRepository $attributeRepository
    ) {}

    /**
     * 优化的产品查询方法
     */
    public function getOptimizedProducts(array $filters = []): Collection
    {
        $currentChannel = core()->getCurrentChannel();
        $customerGroup = app('Webkul\Customer\Repositories\CustomerRepository')->getCurrentGroup();
        
        // 使用查询构建器而不是Eloquent，避免模型事件和访问器的开销
        $query = DB::table('products')
            ->select('products.*')
            ->distinct();

        // 1. 优化JOIN操作 - 使用INNER JOIN替代LEFT JOIN（如果可能）
        $query->join('product_channels', function ($join) use ($currentChannel) {
            $join->on('products.id', '=', 'product_channels.product_id')
                 ->where('product_channels.channel_id', '=', $currentChannel->id);
        });

        // 2. 预加载价格索引
        $query->leftJoin('product_price_indices', function ($join) use ($customerGroup) {
            $join->on('products.id', '=', 'product_price_indices.product_id')
                 ->where('product_price_indices.customer_group_id', '=', $customerGroup->id);
        });

        // 3. 使用子查询优化属性查询，避免多次JOIN
        $attributeCodes = ['status', 'visible_individually', 'url_key'];
        $attributes = $this->attributeRepository->whereIn('code', $attributeCodes)->get();
        
        foreach ($attributes as $attribute) {
            $subQuery = DB::table('product_attribute_values')
                ->select('product_id')
                ->where('attribute_id', $attribute->id);
                
            switch ($attribute->code) {
                case 'status':
                case 'visible_individually':
                    $subQuery->where('boolean_value', 1);
                    break;
                case 'url_key':
                    $subQuery->whereNotNull('text_value');
                    break;
            }
            
            $query->whereIn('products.id', $subQuery);
        }

        // 4. 应用额外的过滤条件
        if (isset($filters['new']) && $filters['new']) {
            $newAttribute = $this->attributeRepository->findOneByField('code', 'new');
            if ($newAttribute) {
                $query->whereExists(function ($q) use ($newAttribute) {
                    $q->select(DB::raw(1))
                      ->from('product_attribute_values')
                      ->whereColumn('product_attribute_values.product_id', 'products.id')
                      ->where('product_attribute_values.attribute_id', $newAttribute->id)
                      ->where('product_attribute_values.boolean_value', 1);
                });
            }
        }

        // 5. 应用排序
        $sortAttribute = $filters['sort'] ?? 'created_at';
        $sortDirection = $filters['order'] ?? 'desc';
        
        if ($sortAttribute === 'price') {
            $query->orderBy('product_price_indices.min_price', $sortDirection);
        } else {
            $query->orderBy("products.$sortAttribute", $sortDirection);
        }

        // 6. 应用分页
        $limit = $filters['limit'] ?? 10;
        $page = $filters['page'] ?? 1;
        $query->limit($limit)->offset(($page - 1) * $limit);

        // 7. 执行查询并转换为Collection
        $products = collect($query->get());
        
        // 8. 批量预加载关联数据
        return $this->preloadRelations($products);
    }

    /**
     * 批量预加载所有关联数据
     */
    protected function preloadRelations(Collection $products): Collection
    {
        if ($products->isEmpty()) {
            return $products;
        }

        $productIds = $products->pluck('id')->toArray();
        
        // 批量加载所有关联数据
        $relations = [
            'images' => $this->batchLoadImages($productIds),
            'videos' => $this->batchLoadVideos($productIds),
            'categories' => $this->batchLoadCategories($productIds),
            'attributeValues' => $this->batchLoadAttributeValues($productIds),
            'priceIndices' => $this->batchLoadPriceIndices($productIds),
            'inventoryIndices' => $this->batchLoadInventoryIndices($productIds),
            'variants' => $this->batchLoadVariants($productIds),
            'reviews' => $this->batchLoadReviews($productIds),
        ];

        // 将关联数据附加到产品
        return $products->map(function ($product) use ($relations) {
            $productId = $product->id;
            
            $product->images = $relations['images']->get($productId, collect());
            $product->videos = $relations['videos']->get($productId, collect());
            $product->categories = $relations['categories']->get($productId, collect());
            $product->attribute_values = $relations['attributeValues']->get($productId, collect());
            $product->price_indices = $relations['priceIndices']->get($productId, collect());
            $product->inventory_indices = $relations['inventoryIndices']->get($productId, collect());
            $product->variants = $relations['variants']->get($productId, collect());
            $product->reviews = $relations['reviews']->get($productId, collect());
            
            return $product;
        });
    }

    /**
     * 批量加载图片
     */
    protected function batchLoadImages(array $productIds): Collection
    {
        return DB::table('product_images')
            ->whereIn('product_id', $productIds)
            ->orderBy('position')
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载视频
     */
    protected function batchLoadVideos(array $productIds): Collection
    {
        return DB::table('product_videos')
            ->whereIn('product_id', $productIds)
            ->orderBy('position')
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载分类
     */
    protected function batchLoadCategories(array $productIds): Collection
    {
        return DB::table('categories')
            ->join('product_categories', 'categories.id', '=', 'product_categories.category_id')
            ->join('category_translations', function ($join) {
                $join->on('categories.id', '=', 'category_translations.category_id')
                     ->where('category_translations.locale', '=', app()->getLocale());
            })
            ->whereIn('product_categories.product_id', $productIds)
            ->select('categories.*', 'category_translations.*', 'product_categories.product_id')
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载属性值
     */
    protected function batchLoadAttributeValues(array $productIds): Collection
    {
        return DB::table('product_attribute_values')
            ->join('attributes', 'product_attribute_values.attribute_id', '=', 'attributes.id')
            ->whereIn('product_attribute_values.product_id', $productIds)
            ->select('product_attribute_values.*', 'attributes.code as attribute_code')
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载价格索引
     */
    protected function batchLoadPriceIndices(array $productIds): Collection
    {
        $customerGroup = app('Webkul\Customer\Repositories\CustomerRepository')->getCurrentGroup();
        
        return DB::table('product_price_indices')
            ->whereIn('product_id', $productIds)
            ->where('customer_group_id', $customerGroup->id)
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载库存索引
     */
    protected function batchLoadInventoryIndices(array $productIds): Collection
    {
        $currentChannel = core()->getCurrentChannel();
        
        return DB::table('product_inventory_indices')
            ->whereIn('product_id', $productIds)
            ->where('channel_id', $currentChannel->id)
            ->get()
            ->groupBy('product_id');
    }

    /**
     * 批量加载变体
     */
    protected function batchLoadVariants(array $productIds): Collection
    {
        return DB::table('products')
            ->whereIn('parent_id', $productIds)
            ->get()
            ->groupBy('parent_id');
    }

    /**
     * 批量加载评论
     */
    protected function batchLoadReviews(array $productIds): Collection
    {
        return DB::table('product_reviews')
            ->whereIn('product_id', $productIds)
            ->where('status', 'approved')
            ->selectRaw('product_id, COUNT(*) as review_count, AVG(rating) as avg_rating')
            ->groupBy('product_id')
            ->get()
            ->keyBy('product_id');
    }

    /**
     * 优化属性翻译查询
     */
    public function preloadAttributeTranslations(array $attributeIds, string $locale): Collection
    {
        return DB::table('attribute_translations')
            ->whereIn('attribute_id', $attributeIds)
            ->where('locale', $locale)
            ->get()
            ->keyBy('attribute_id');
    }

    /**
     * 优化属性选项翻译查询
     */
    public function preloadAttributeOptionTranslations(array $optionIds, string $locale): Collection
    {
        return DB::table('attribute_option_translations')
            ->whereIn('attribute_option_id', $optionIds)
            ->where('locale', $locale)
            ->get()
            ->keyBy('attribute_option_id');
    }
}