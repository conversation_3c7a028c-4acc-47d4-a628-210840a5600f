<?php

namespace Webkul\MLKWebAPI\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class OptimizeDatabaseCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bagisto:optimize-db 
                            {--analyze : Run ANALYZE TABLE on all optimized tables}
                            {--check : Check if indexes exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database performance by checking indexes and analyzing tables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting database optimization...');

        if ($this->option('check')) {
            $this->checkIndexes();
        }

        if ($this->option('analyze')) {
            $this->analyzeTables();
        }

        if (!$this->option('check') && !$this->option('analyze')) {
            $this->info('Use --check to verify indexes or --analyze to update table statistics');
        }

        $this->info('Database optimization completed!');
    }

    /**
     * Check if all required indexes exist
     */
    protected function checkIndexes()
    {
        $this->info('Checking database indexes...');

        $requiredIndexes = [
            'channels' => ['idx_channels_hostname'],
            'channel_locales' => ['idx_channel_locales_composite'],
            'attribute_translations' => ['idx_attribute_translations_composite'],
            'attribute_option_translations' => ['idx_attribute_option_translations_composite'],
            'product_categories' => ['idx_product_categories_composite', 'idx_product_categories_category'],
            'category_translations' => ['idx_category_translations_composite'],
            'customer_groups' => ['idx_customer_groups_code'],
            'theme_customizations' => ['idx_theme_customizations_composite'],
            'product_attribute_values' => [
                'idx_product_attribute_values_composite',
                'idx_product_attribute_values_attr_locale',
                'idx_product_attribute_values_bool',
                'idx_product_attribute_values_text'
            ],
            'product_price_indices' => ['idx_product_price_indices_composite'],
            'catalog_rule_product_prices' => ['idx_catalog_rule_prices_composite'],
            'product_customer_group_prices' => ['idx_product_customer_group_prices_composite'],
            'product_images' => ['idx_product_images_composite'],
            'product_videos' => ['idx_product_videos_composite'],
            'product_inventory_indices' => ['idx_product_inventory_indices_composite'],
            'attribute_group_mappings' => ['idx_attribute_group_mappings_composite'],
            'product_channels' => ['idx_product_channels_composite', 'idx_product_channels_channel'],
            'product_super_attributes' => ['idx_product_super_attributes_composite'],
            'cms_pages' => ['idx_cms_pages_id'],
            'cms_page_translations' => ['idx_cms_page_translations_composite']
        ];

        $missingIndexes = [];

        foreach ($requiredIndexes as $table => $indexes) {
            if (!Schema::hasTable($table)) {
                $this->warn("Table '$table' does not exist");
                continue;
            }

            foreach ($indexes as $indexName) {
                if (!$this->indexExists($table, $indexName)) {
                    $missingIndexes[] = "$table.$indexName";
                    $this->error("✗ Missing index: $table.$indexName");
                } else {
                    $this->info("✓ Index exists: $table.$indexName");
                }
            }
        }

        if (count($missingIndexes) > 0) {
            $this->error("\nMissing indexes found! Run 'php artisan migrate' to create them.");
        } else {
            $this->info("\nAll required indexes are present!");
        }
    }

    /**
     * Run ANALYZE TABLE on all optimized tables
     */
    protected function analyzeTables()
    {
        if (DB::getDriverName() !== 'mysql') {
            $this->warn('ANALYZE TABLE is only supported for MySQL/MariaDB');
            return;
        }

        $this->info('Analyzing tables to update statistics...');

        $tables = [
            'channels', 'channel_locales', 'attribute_families', 'attribute_translations',
            'attribute_option_translations', 'product_categories', 'category_translations',
            'customer_groups', 'theme_customizations', 'product_attribute_values',
            'product_price_indices', 'catalog_rule_product_prices', 'product_customer_group_prices',
            'product_images', 'product_videos', 'product_inventory_indices', 'personal_access_tokens',
            'attribute_group_mappings', 'product_channels', 'product_super_attributes',
            'cms_pages', 'cms_page_translations'
        ];

        $bar = $this->output->createProgressBar(count($tables));
        $bar->start();

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                try {
                    DB::statement("ANALYZE TABLE $table");
                    $bar->advance();
                } catch (\Exception $e) {
                    $this->error("\nFailed to analyze table '$table': " . $e->getMessage());
                }
            }
        }

        $bar->finish();
        $this->info("\n\nTable analysis completed!");
    }

    /**
     * Check if an index exists
     */
    protected function indexExists($table, $indexName): bool
    {
        $indexes = DB::select("SHOW INDEX FROM $table");
        foreach ($indexes as $index) {
            if ($index->Key_name === $indexName) {
                return true;
            }
        }
        return false;
    }
}