<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. channels表索引优化
        Schema::table('channels', function (Blueprint $table) {
            // 为hostname查询添加索引（日志显示5.15秒）
            $table->index('hostname', 'idx_channels_hostname');
        });

        // 2. channel_locales关联表索引
        Schema::table('channel_locales', function (Blueprint $table) {
            // 复合索引优化JOIN查询（日志显示3.58秒）
            $table->index(['channel_id', 'locale_id'], 'idx_channel_locales_composite');
        });

        // 3. attribute_families表索引
        Schema::table('attribute_families', function (Blueprint $table) {
            // 主键应该已有索引，但确保覆盖索引存在
            if (!$this->indexExists('attribute_families', 'PRIMARY')) {
                $table->index('id', 'idx_attribute_families_id');
            }
        });

        // 4. attribute_translations表索引
        Schema::table('attribute_translations', function (Blueprint $table) {
            // 复合索引避免重复查询（日志显示大量重复查询）
            $table->index(['attribute_id', 'locale'], 'idx_attribute_translations_composite');
        });

        // 5. attribute_option_translations表索引
        Schema::table('attribute_option_translations', function (Blueprint $table) {
            // 复合索引优化翻译查询
            $table->index(['attribute_option_id', 'locale'], 'idx_attribute_option_translations_composite');
        });

        // 6. product_categories关联表索引
        Schema::table('product_categories', function (Blueprint $table) {
            // 优化产品分类关联查询（日志显示4.17秒）
            $table->index(['product_id', 'category_id'], 'idx_product_categories_composite');
            $table->index('category_id', 'idx_product_categories_category');
        });

        // 7. category_translations表索引
        Schema::table('category_translations', function (Blueprint $table) {
            // 优化分类翻译查询（日志显示2.18秒）
            $table->index(['category_id', 'locale'], 'idx_category_translations_composite');
        });

        // 8. customer_groups表索引
        Schema::table('customer_groups', function (Blueprint $table) {
            // 优化按code查询（日志显示2.96秒）
            $table->index('code', 'idx_customer_groups_code');
        });

        // 9. theme_customizations表索引
        Schema::table('theme_customizations', function (Blueprint $table) {
            // 复合索引优化主题查询（日志显示2.61秒）
            $table->index(['channel_id', 'status'], 'idx_theme_customizations_composite');
        });

        // 10. product_attribute_values表索引
        Schema::table('product_attribute_values', function (Blueprint $table) {
            // 复合索引优化属性值查询
            $table->index(['product_id', 'attribute_id'], 'idx_product_attribute_values_composite');
            $table->index(['attribute_id', 'locale'], 'idx_product_attribute_values_attr_locale');
            
            // 针对常用的筛选属性添加索引
            $table->index(['attribute_id', 'boolean_value'], 'idx_product_attribute_values_bool');
            $table->index(['attribute_id', 'text_value'], 'idx_product_attribute_values_text');
        });

        // 11. product_price_indices表索引
        Schema::table('product_price_indices', function (Blueprint $table) {
            // 复合索引优化价格查询
            $table->index(['product_id', 'customer_group_id', 'channel_id'], 'idx_product_price_indices_composite');
        });

        // 12. catalog_rule_product_prices表索引
        Schema::table('catalog_rule_product_prices', function (Blueprint $table) {
            // 复合索引优化规则价格查询（日志显示2.36秒）
            $table->index(['product_id', 'channel_id', 'customer_group_id', 'rule_date'], 'idx_catalog_rule_prices_composite');
        });

        // 13. product_customer_group_prices表索引
        Schema::table('product_customer_group_prices', function (Blueprint $table) {
            // 复合索引优化客户组价格查询（日志显示2.49秒）
            $table->index(['product_id', 'customer_group_id'], 'idx_product_customer_group_prices_composite');
        });

        // 14. product_images表索引
        Schema::table('product_images', function (Blueprint $table) {
            // 优化产品图片查询（日志显示3秒）
            $table->index(['product_id', 'position'], 'idx_product_images_composite');
        });

        // 15. product_videos表索引
        Schema::table('product_videos', function (Blueprint $table) {
            // 优化产品视频查询（日志显示2.34秒）
            $table->index(['product_id', 'position'], 'idx_product_videos_composite');
        });

        // 16. product_inventory_indices表索引
        Schema::table('product_inventory_indices', function (Blueprint $table) {
            // 复合索引优化库存查询
            $table->index(['product_id', 'channel_id'], 'idx_product_inventory_indices_composite');
        });

        // 17. personal_access_tokens表索引
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            // 确保token查询快速
            if (!$this->indexExists('personal_access_tokens', 'personal_access_tokens_token_unique')) {
                $table->index('token', 'idx_personal_access_tokens_token');
            }
        });

        // 18. attribute_group_mappings表索引
        Schema::table('attribute_group_mappings', function (Blueprint $table) {
            // 优化属性组映射查询（用于JOIN操作）
            $table->index(['attribute_id', 'attribute_group_id'], 'idx_attribute_group_mappings_composite');
        });

        // 19. product_channels表索引
        Schema::table('product_channels', function (Blueprint $table) {
            // 优化产品渠道关联查询
            $table->index(['product_id', 'channel_id'], 'idx_product_channels_composite');
            $table->index('channel_id', 'idx_product_channels_channel');
        });

        // 20. product_super_attributes表索引
        Schema::table('product_super_attributes', function (Blueprint $table) {
            // 优化可配置产品属性查询
            $table->index(['product_id', 'attribute_id'], 'idx_product_super_attributes_composite');
        });

        // 21. cms_pages表索引
        Schema::table('cms_pages', function (Blueprint $table) {
            // 优化CMS页面查询（日志显示1.99秒）
            $table->index('id', 'idx_cms_pages_id');
        });

        // 22. cms_page_translations表索引
        Schema::table('cms_page_translations', function (Blueprint $table) {
            // 优化CMS页面翻译查询（日志显示2.12秒）
            $table->index(['cms_page_id', 'locale'], 'idx_cms_page_translations_composite');
        });

        // 更新数据库统计信息
        if (DB::getDriverName() === 'mysql') {
            DB::statement('ANALYZE TABLE channels, channel_locales, attribute_families, attribute_translations, 
                          attribute_option_translations, product_categories, category_translations, customer_groups,
                          theme_customizations, product_attribute_values, product_price_indices, catalog_rule_product_prices,
                          product_customer_group_prices, product_images, product_videos, product_inventory_indices,
                          personal_access_tokens, attribute_group_mappings, product_channels, product_super_attributes,
                          cms_pages, cms_page_translations');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除所有创建的索引
        $indexes = [
            'channels' => ['idx_channels_hostname'],
            'channel_locales' => ['idx_channel_locales_composite'],
            'attribute_families' => ['idx_attribute_families_id'],
            'attribute_translations' => ['idx_attribute_translations_composite'],
            'attribute_option_translations' => ['idx_attribute_option_translations_composite'],
            'product_categories' => ['idx_product_categories_composite', 'idx_product_categories_category'],
            'category_translations' => ['idx_category_translations_composite'],
            'customer_groups' => ['idx_customer_groups_code'],
            'theme_customizations' => ['idx_theme_customizations_composite'],
            'product_attribute_values' => [
                'idx_product_attribute_values_composite',
                'idx_product_attribute_values_attr_locale',
                'idx_product_attribute_values_bool',
                'idx_product_attribute_values_text'
            ],
            'product_price_indices' => ['idx_product_price_indices_composite'],
            'catalog_rule_product_prices' => ['idx_catalog_rule_prices_composite'],
            'product_customer_group_prices' => ['idx_product_customer_group_prices_composite'],
            'product_images' => ['idx_product_images_composite'],
            'product_videos' => ['idx_product_videos_composite'],
            'product_inventory_indices' => ['idx_product_inventory_indices_composite'],
            'personal_access_tokens' => ['idx_personal_access_tokens_token'],
            'attribute_group_mappings' => ['idx_attribute_group_mappings_composite'],
            'product_channels' => ['idx_product_channels_composite', 'idx_product_channels_channel'],
            'product_super_attributes' => ['idx_product_super_attributes_composite'],
            'cms_pages' => ['idx_cms_pages_id'],
            'cms_page_translations' => ['idx_cms_page_translations_composite']
        ];

        foreach ($indexes as $table => $indexNames) {
            Schema::table($table, function (Blueprint $table) use ($indexNames) {
                foreach ($indexNames as $indexName) {
                    if ($this->indexExists($table->getTable(), $indexName)) {
                        $table->dropIndex($indexName);
                    }
                }
            });
        }
    }

    /**
     * 检查索引是否存在
     */
    private function indexExists($table, $indexName): bool
    {
        $indexes = DB::select("SHOW INDEX FROM $table");
        foreach ($indexes as $index) {
            if ($index->Key_name === $indexName) {
                return true;
            }
        }
        return false;
    }
};